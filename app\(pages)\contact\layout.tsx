import { Metadata } from "next";

export const metadata: Metadata = {
  metadataBase: new URL('https://www.flerid.in'),
  title: "Contact Flerid Technologies | Web Development Company in Silchar, Assam, India",
  description: "Contact Flerid Technologies for web development, mobile app development, and digital marketing services in Silchar, Assam, India. Get a free consultation for your project. Phone: +91-6003351943, Email: <EMAIL>",
  keywords: [
    "contact web development company Silchar",
    "Flerid Technologies contact",
    "web development consultation Assam",
    "software development company contact India",
    "digital marketing agency contact Silchar",
    "mobile app development consultation",
    "Silchar tech company contact",
    "Assam web development services",
    "India software company contact",
    "free consultation web development"
  ],
  authors: [{ name: "Flerid Technologies" }],
  creator: "Flerid Technologies",
  publisher: "Flerid Technologies",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://www.flerid.in/contact",
    title: "Contact Flerid Technologies | Web Development Company in Silchar, Assam, India",
    description: "Contact Flerid Technologies for web development, mobile app development, and digital marketing services in Silchar, Assam, India. Get a free consultation.",
    siteName: "Flerid Technologies",
    images: [
      {
        url: "/og-contact-flerid.jpg",
        width: 1200,
        height: 630,
        alt: "Contact Flerid Technologies - Web Development Company in Silchar, Assam",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Contact Flerid Technologies | Web Development Company in Silchar, Assam, India",
    description: "Contact Flerid Technologies for web development and digital marketing services in Silchar, Assam, India.",
    images: ["/og-contact-flerid.jpg"],
    creator: "@fleridtech",
  },
  alternates: {
    canonical: "https://www.flerid.in/contact",
  },
  other: {
    "geo.region": "IN-AS",
    "geo.placename": "Silchar, Assam, India",
    "geo.position": "24.8333;92.7789",
    "ICBM": "24.8333, 92.7789",
    "business:contact_data:street_address": "Silchar",
    "business:contact_data:locality": "Silchar",
    "business:contact_data:region": "Assam",
    "business:contact_data:postal_code": "788001",
    "business:contact_data:country_name": "India",
    "business:contact_data:phone_number": "+91-6003351943",
    "business:contact_data:email": "<EMAIL>",
  },
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Enhanced LocalBusiness schema for contact page
  const contactPageSchema = {
    "@context": "https://schema.org",
    "@type": ["LocalBusiness", "ProfessionalService"],
    "@id": "https://www.flerid.in/#organization",
    "name": "Flerid Technologies",
    "alternateName": "Flerid",
    "description": "Leading web development and digital marketing company in Silchar, Assam, India providing custom web development, mobile app development, SEO services, and digital marketing solutions.",
    "url": "https://www.flerid.in",
    "telephone": ["+91-6003351943", "+91-7002135973", "+91-6901527553"],
    "email": "<EMAIL>",
    "foundingDate": "2022",
    "slogan": "Transforming Ideas into Digital Reality",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Silchar",
      "addressRegion": "Assam",
      "postalCode": "788001",
      "addressCountry": "IN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "24.8333",
      "longitude": "92.7789"
    },
    "openingHours": "Mo-Sa 09:00-18:00",
    "priceRange": "$$",
    "currenciesAccepted": "INR, USD",
    "paymentAccepted": "Cash, Credit Card, Bank Transfer, UPI",
    "areaServed": [
      {
        "@type": "Country",
        "name": "India"
      },
      {
        "@type": "State", 
        "name": "Assam"
      },
      {
        "@type": "City",
        "name": "Silchar"
      }
    ],
    "serviceArea": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": "24.8333",
        "longitude": "92.7789"
      },
      "geoRadius": "50000"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Web Development and Digital Marketing Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Web Development",
            "description": "Custom web development services using React, Next.js, Node.js",
            "provider": {
              "@type": "LocalBusiness",
              "name": "Flerid Technologies"
            }
          }
        },
        {
          "@type": "Offer", 
          "itemOffered": {
            "@type": "Service",
            "name": "Mobile App Development",
            "description": "Native and cross-platform mobile app development using React Native and Flutter",
            "provider": {
              "@type": "LocalBusiness",
              "name": "Flerid Technologies"
            }
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service", 
            "name": "Digital Marketing",
            "description": "SEO, social media marketing, and digital advertising services",
            "provider": {
              "@type": "LocalBusiness",
              "name": "Flerid Technologies"
            }
          }
        }
      ]
    },
    "sameAs": [
      "https://www.linkedin.com/company/flerid-technologies",
      "https://www.facebook.com/fleridtechnologies", 
      "https://twitter.com/fleridtech"
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(contactPageSchema) }}
      />
      {children}
    </>
  );
}
