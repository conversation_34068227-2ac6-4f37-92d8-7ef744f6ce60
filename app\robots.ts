import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/_next/',
          '/test-*',
          '/debug',
          '/private/',
          '/*.json$',
          '/temp/'
        ],
        crawlDelay: 1,
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/test-*',
          '/debug',
          '/private/'
        ],
        crawlDelay: 0,
      },
      {
        userAgent: 'Bingbot',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/test-*',
          '/debug',
          '/private/'
        ],
        crawlDelay: 1,
      },
      {
        userAgent: 'facebookexternalhit',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/private/'
        ],
      },
      {
        userAgent: 'Twitterbot',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/private/'
        ],
      },
    ],
    sitemap: [
      'https://www.flerid.in/sitemap.xml',
      'https://www.flerid.in/sitemap-blogs.xml',
      'https://www.flerid.in/sitemap-services.xml'
    ],
    host: 'https://www.flerid.in'
  }
}
