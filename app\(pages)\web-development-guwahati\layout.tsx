import { Metadata } from "next";

export const metadata: Metadata = {
  metadataBase: new URL('https://www.flerid.in'),
  title: "Web Development Services in Guwahati, Assam | Flerid Technologies",
  description: "Professional web development, mobile app development, and digital marketing services in Guwahati, Assam. Flerid Technologies serves businesses across Guwahati with custom software solutions, React development, and SEO services.",
  keywords: [
    "web development Guwahati",
    "web development services Guwahati Assam",
    "software development company Guwahati",
    "mobile app development Guwahati",
    "digital marketing Guwahati",
    "SEO services Guwahati",
    "React development Guwahati",
    "Node.js development Guwahati",
    "e-commerce development Guwahati",
    "website design Guwahati",
    "custom software Guwahati",
    "web development company Assam",
    "Flerid Technologies Guwahati",
    "Silchar to Guwahati web services"
  ],
  authors: [{ name: "Flerid Technologies" }],
  creator: "Flerid Technologies",
  publisher: "Flerid Technologies",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://www.flerid.in/web-development-guwahati",
    title: "Web Development Services in Guwahati, Assam | Flerid Technologies",
    description: "Professional web development, mobile app development, and digital marketing services in Guwahati, Assam. Custom software solutions for Guwahati businesses.",
    siteName: "Flerid Technologies",
    images: [
      {
        url: "/og-guwahati-services.jpg",
        width: 1200,
        height: 630,
        alt: "Web Development Services in Guwahati, Assam by Flerid Technologies",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Web Development Services in Guwahati, Assam | Flerid Technologies",
    description: "Professional web development and digital marketing services in Guwahati, Assam by Flerid Technologies.",
    images: ["/og-guwahati-services.jpg"],
    creator: "@fleridtech",
  },
  alternates: {
    canonical: "https://www.flerid.in/web-development-guwahati",
  },
  other: {
    "geo.region": "IN-AS",
    "geo.placename": "Guwahati, Assam, India",
    "geo.position": "26.1445;91.7362",
    "ICBM": "26.1445, 91.7362",
    "business:contact_data:locality": "Guwahati",
    "business:contact_data:region": "Assam",
    "business:contact_data:country_name": "India",
  },
};

export default function GuwahatiLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Location-specific structured data
  const guwahatiServiceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Web Development Services in Guwahati",
    "description": "Professional web development, mobile app development, and digital marketing services for businesses in Guwahati, Assam",
    "provider": {
      "@type": "LocalBusiness",
      "name": "Flerid Technologies",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Silchar",
        "addressRegion": "Assam",
        "addressCountry": "IN"
      },
      "telephone": "+91-**********",
      "url": "https://www.flerid.in"
    },
    "areaServed": {
      "@type": "City",
      "name": "Guwahati",
      "containedInPlace": {
        "@type": "State",
        "name": "Assam",
        "containedInPlace": {
          "@type": "Country",
          "name": "India"
        }
      }
    },
    "serviceType": [
      "Web Development",
      "Mobile App Development", 
      "Digital Marketing",
      "SEO Services",
      "E-commerce Development"
    ],
    "offers": {
      "@type": "Offer",
      "availability": "https://schema.org/InStock",
      "priceCurrency": "INR",
      "description": "Custom web development and digital marketing solutions for Guwahati businesses"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(guwahatiServiceSchema) }}
      />
      {children}
    </>
  );
}
