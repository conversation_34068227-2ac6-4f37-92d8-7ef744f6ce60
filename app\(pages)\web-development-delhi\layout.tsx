import { Metadata } from "next";

export const metadata: Metadata = {
  metadataBase: new URL('https://www.flerid.in'),
  title: "Web Development Company in Delhi | Custom Software Solutions India | Flerid Technologies",
  description: "Leading web development company serving Delhi NCR with custom software solutions, mobile app development, and digital marketing services. Flerid Technologies delivers enterprise-grade solutions from Assam, India at competitive prices.",
  keywords: [
    "web development company Delhi",
    "software development Delhi NCR",
    "mobile app development Delhi",
    "digital marketing Delhi",
    "custom software development Delhi",
    "React development Delhi",
    "Node.js development Delhi",
    "enterprise web solutions Delhi",
    "e-commerce development Delhi",
    "SEO services Delhi",
    "web development services Delhi",
    "Delhi web development company",
    "Flerid Technologies Delhi",
    "Northeast India tech company",
    "affordable web development Delhi"
  ],
  authors: [{ name: "Flerid Technologies" }],
  creator: "Flerid Technologies",
  publisher: "Flerid Technologies",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://www.flerid.in/web-development-delhi",
    title: "Web Development Company in Delhi | Custom Software Solutions India | Flerid Technologies",
    description: "Leading web development company serving Delhi NCR with custom software solutions, mobile app development, and digital marketing services from Assam, India.",
    siteName: "Flerid Technologies",
    images: [
      {
        url: "/og-delhi-services.jpg",
        width: 1200,
        height: 630,
        alt: "Web Development Company in Delhi - Flerid Technologies",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Web Development Company in Delhi | Custom Software Solutions India | Flerid Technologies",
    description: "Leading web development company serving Delhi NCR with custom software solutions and digital marketing services.",
    images: ["/og-delhi-services.jpg"],
    creator: "@fleridtech",
  },
  alternates: {
    canonical: "https://www.flerid.in/web-development-delhi",
  },
  other: {
    "geo.region": "IN-DL",
    "geo.placename": "Delhi, India",
    "geo.position": "28.7041;77.1025",
    "ICBM": "28.7041, 77.1025",
    "business:contact_data:locality": "Delhi",
    "business:contact_data:region": "Delhi",
    "business:contact_data:country_name": "India",
  },
};

export default function DelhiLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Location-specific structured data for Delhi
  const delhiServiceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Web Development Services in Delhi",
    "description": "Professional web development, mobile app development, and digital marketing services for businesses in Delhi NCR, India",
    "provider": {
      "@type": "LocalBusiness",
      "name": "Flerid Technologies",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Silchar",
        "addressRegion": "Assam",
        "addressCountry": "IN"
      },
      "telephone": "+91-**********",
      "url": "https://www.flerid.in",
      "email": "<EMAIL>"
    },
    "areaServed": [
      {
        "@type": "City",
        "name": "Delhi",
        "containedInPlace": {
          "@type": "Country",
          "name": "India"
        }
      },
      {
        "@type": "City",
        "name": "New Delhi",
        "containedInPlace": {
          "@type": "Country",
          "name": "India"
        }
      },
      {
        "@type": "City",
        "name": "Gurgaon",
        "containedInPlace": {
          "@type": "State",
          "name": "Haryana",
          "containedInPlace": {
            "@type": "Country",
            "name": "India"
          }
        }
      },
      {
        "@type": "City",
        "name": "Noida",
        "containedInPlace": {
          "@type": "State",
          "name": "Uttar Pradesh",
          "containedInPlace": {
            "@type": "Country",
            "name": "India"
          }
        }
      }
    ],
    "serviceType": [
      "Enterprise Web Development",
      "Mobile App Development", 
      "Digital Marketing",
      "SEO Services",
      "E-commerce Development",
      "Custom Software Development"
    ],
    "offers": {
      "@type": "Offer",
      "availability": "https://schema.org/InStock",
      "priceCurrency": "INR",
      "description": "Enterprise-grade web development and digital marketing solutions for Delhi NCR businesses"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Delhi Web Development Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Enterprise Web Development",
            "description": "Scalable web applications for Delhi's corporate sector"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Mobile App Development",
            "description": "iOS and Android apps for Delhi market"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Digital Marketing",
            "description": "Comprehensive digital marketing for Delhi businesses"
          }
        }
      ]
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(delhiServiceSchema) }}
      />
      {children}
    </>
  );
}
