import { Metadata } from "next";

export const metadata: Metadata = {
  metadataBase: new URL('https://www.flerid.in'),
  title: "Web Development Company in Mumbai | Enterprise Software Solutions India | Flerid Technologies",
  description: "Leading web development company serving Mumbai with enterprise software solutions, fintech app development, and digital marketing services. Flerid Technologies delivers scalable solutions for Mumbai's corporate and financial sectors from Assam, India.",
  keywords: [
    "web development company Mumbai",
    "software development Mumbai Maharashtra",
    "enterprise web solutions Mumbai",
    "fintech app development Mumbai",
    "digital marketing Mumbai",
    "custom software development Mumbai",
    "React development Mumbai",
    "Node.js development Mumbai",
    "e-commerce development Mumbai",
    "SEO services Mumbai",
    "web development services Mumbai",
    "Mumbai web development company",
    "Flerid Technologies Mumbai",
    "corporate web solutions Mumbai",
    "financial software development Mumbai"
  ],
  authors: [{ name: "Flerid Technologies" }],
  creator: "Flerid Technologies",
  publisher: "Flerid Technologies",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://www.flerid.in/web-development-mumbai",
    title: "Web Development Company in Mumbai | Enterprise Software Solutions India | Flerid Technologies",
    description: "Leading web development company serving Mumbai with enterprise software solutions, fintech app development, and digital marketing services from Assam, India.",
    siteName: "Flerid Technologies",
    images: [
      {
        url: "/og-mumbai-services.jpg",
        width: 1200,
        height: 630,
        alt: "Web Development Company in Mumbai - Flerid Technologies",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Web Development Company in Mumbai | Enterprise Software Solutions India | Flerid Technologies",
    description: "Leading web development company serving Mumbai with enterprise software solutions and digital marketing services.",
    images: ["/og-mumbai-services.jpg"],
    creator: "@fleridtech",
  },
  alternates: {
    canonical: "https://www.flerid.in/web-development-mumbai",
  },
  other: {
    "geo.region": "IN-MH",
    "geo.placename": "Mumbai, Maharashtra, India",
    "geo.position": "19.0760;72.8777",
    "ICBM": "19.0760, 72.8777",
    "business:contact_data:locality": "Mumbai",
    "business:contact_data:region": "Maharashtra",
    "business:contact_data:country_name": "India",
  },
};

export default function MumbaiLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Location-specific structured data for Mumbai
  const mumbaiServiceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Web Development Services in Mumbai",
    "description": "Professional web development, enterprise software solutions, and digital marketing services for businesses in Mumbai, Maharashtra, India",
    "provider": {
      "@type": "LocalBusiness",
      "name": "Flerid Technologies",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Silchar",
        "addressRegion": "Assam",
        "addressCountry": "IN"
      },
      "telephone": "+91-**********",
      "url": "https://www.flerid.in",
      "email": "<EMAIL>"
    },
    "areaServed": [
      {
        "@type": "City",
        "name": "Mumbai",
        "containedInPlace": {
          "@type": "State",
          "name": "Maharashtra",
          "containedInPlace": {
            "@type": "Country",
            "name": "India"
          }
        }
      },
      {
        "@type": "City",
        "name": "Navi Mumbai",
        "containedInPlace": {
          "@type": "State",
          "name": "Maharashtra",
          "containedInPlace": {
            "@type": "Country",
            "name": "India"
          }
        }
      },
      {
        "@type": "City",
        "name": "Thane",
        "containedInPlace": {
          "@type": "State",
          "name": "Maharashtra",
          "containedInPlace": {
            "@type": "Country",
            "name": "India"
          }
        }
      }
    ],
    "serviceType": [
      "Enterprise Web Development",
      "Fintech App Development", 
      "Digital Marketing",
      "SEO Services",
      "E-commerce Development",
      "Custom Software Development",
      "Corporate Web Solutions"
    ],
    "offers": {
      "@type": "Offer",
      "availability": "https://schema.org/InStock",
      "priceCurrency": "INR",
      "description": "Enterprise-grade web development and digital marketing solutions for Mumbai businesses"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Mumbai Enterprise Web Development Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Enterprise Web Development",
            "description": "Scalable web applications for Mumbai's corporate sector"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Fintech App Development",
            "description": "Secure mobile applications for Mumbai's financial sector"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Digital Marketing",
            "description": "Comprehensive digital marketing for Mumbai enterprises"
          }
        }
      ]
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(mumbaiServiceSchema) }}
      />
      {children}
    </>
  );
}
