import { Metadata } from "next";

export const metadata: Metadata = {
  metadataBase: new URL('https://www.flerid.in'),
  title: "Web Development Company in Bangalore | Startup & Tech Solutions India | Flerid Technologies",
  description: "Leading web development company serving Bangalore's startup ecosystem with MVP development, SaaS platforms, and tech solutions. Flerid Technologies delivers innovative solutions for India's Silicon Valley from Assam, India.",
  keywords: [
    "web development company Bangalore",
    "startup web development Bangalore",
    "MVP development Bangalore Karnataka",
    "SaaS development Bangalore",
    "tech startup solutions Bangalore",
    "React development Bangalore",
    "Node.js development Bangalore",
    "mobile app development Bangalore",
    "digital marketing Bangalore",
    "custom software development Bangalore",
    "web development services Bangalore",
    "Bangalore web development company",
    "Flerid Technologies Bangalore",
    "startup tech solutions India",
    "agile development Bangalore"
  ],
  authors: [{ name: "Flerid Technologies" }],
  creator: "Flerid Technologies",
  publisher: "Flerid Technologies",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://www.flerid.in/web-development-bangalore",
    title: "Web Development Company in Bangalore | Startup & Tech Solutions India | Flerid Technologies",
    description: "Leading web development company serving Bangalore's startup ecosystem with MVP development, SaaS platforms, and tech solutions from Assam, India.",
    siteName: "Flerid Technologies",
    images: [
      {
        url: "/og-bangalore-services.jpg",
        width: 1200,
        height: 630,
        alt: "Web Development Company in Bangalore - Flerid Technologies",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Web Development Company in Bangalore | Startup & Tech Solutions India | Flerid Technologies",
    description: "Leading web development company serving Bangalore's startup ecosystem with innovative tech solutions.",
    images: ["/og-bangalore-services.jpg"],
    creator: "@fleridtech",
  },
  alternates: {
    canonical: "https://www.flerid.in/web-development-bangalore",
  },
  other: {
    "geo.region": "IN-KA",
    "geo.placename": "Bangalore, Karnataka, India",
    "geo.position": "12.9716;77.5946",
    "ICBM": "12.9716, 77.5946",
    "business:contact_data:locality": "Bangalore",
    "business:contact_data:region": "Karnataka",
    "business:contact_data:country_name": "India",
  },
};

export default function BangaloreLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Location-specific structured data for Bangalore
  const bangaloreServiceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Web Development Services in Bangalore",
    "description": "Professional web development, startup MVP development, and tech solutions for businesses in Bangalore, Karnataka, India",
    "provider": {
      "@type": "LocalBusiness",
      "name": "Flerid Technologies",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Silchar",
        "addressRegion": "Assam",
        "addressCountry": "IN"
      },
      "telephone": "+91-**********",
      "url": "https://www.flerid.in",
      "email": "<EMAIL>"
    },
    "areaServed": [
      {
        "@type": "City",
        "name": "Bangalore",
        "alternateName": "Bengaluru",
        "containedInPlace": {
          "@type": "State",
          "name": "Karnataka",
          "containedInPlace": {
            "@type": "Country",
            "name": "India"
          }
        }
      },
      {
        "@type": "City",
        "name": "Whitefield",
        "containedInPlace": {
          "@type": "State",
          "name": "Karnataka",
          "containedInPlace": {
            "@type": "Country",
            "name": "India"
          }
        }
      },
      {
        "@type": "City",
        "name": "Electronic City",
        "containedInPlace": {
          "@type": "State",
          "name": "Karnataka",
          "containedInPlace": {
            "@type": "Country",
            "name": "India"
          }
        }
      }
    ],
    "serviceType": [
      "Startup Web Development",
      "MVP Development", 
      "SaaS Platform Development",
      "Mobile App Development",
      "Digital Marketing",
      "Tech Product Development",
      "Custom Software Development"
    ],
    "offers": {
      "@type": "Offer",
      "availability": "https://schema.org/InStock",
      "priceCurrency": "INR",
      "description": "Startup-focused web development and tech solutions for Bangalore companies"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Bangalore Startup Web Development Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Startup Web Solutions",
            "description": "Rapid MVP development for Bangalore startups"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "SaaS Platform Development",
            "description": "Cloud-based software solutions for tech companies"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Growth Marketing",
            "description": "Data-driven digital marketing for tech startups"
          }
        }
      ]
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(bangaloreServiceSchema) }}
      />
      {children}
    </>
  );
}
